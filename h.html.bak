<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prayer Timer with <PERSON> Dissolve</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --gold-primary: #DAA520;
            --gold-secondary: #FFD700;
            --gold-tertiary: #F0E68C;
            --brown-primary: #8B4513;
            --brown-secondary: #A0522D;
            --brown-tertiary: #CD853F;
            --green-primary: #2C5530;
            --green-secondary: #3D8B40;
            --blue-primary: #1a3a5f;
            --white-primary: #FFFDF7;
            --white-secondary: #F8F4E9;
            --shadow-light: 0 4px 20px rgba(0,0,0,0.08);
            --shadow-medium: 0 8px 30px rgba(0,0,0,0.15);
            --shadow-heavy: 0 12px 40px rgba(0,0,0,0.2);
            --prayer-point-size: 60px;
            --atmosphere-opacity: 0.6;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #0c0c0c, var(--blue-primary), #16213e;
            color: var(--white-primary);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
            overflow: hidden;
        }
        
        .container {
            max-width: 800px;
            width: 100%;
            text-align: center;
        }
        
        h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, var(--gold-tertiary), var(--gold-secondary), var(--gold-primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(218, 165, 32, 0.3);
            animation: titleGlow 3s ease-in-out infinite alternate;
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.8;
            color: var(--gold-tertiary);
        }
        
        /* Enhanced Molecular Dissolve Container */
        .molecular-dissolve-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 20px 0;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 
                0 20px 60px rgba(0, 0, 0, 0.6),
                inset 0 0 30px rgba(255, 255, 255, 0.05);
            transition: transform 0.3s ease;
        }
        
        /* Background particle field */
        .particle-field {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            opacity: 0.3;
        }
        
        .bg-particle {
            position: absolute;
            background: radial-gradient(circle, rgba(218, 165, 32, 0.6), transparent);
            border-radius: 50%;
            animation: floatParticle 8s infinite linear;
        }
        
        /* Content layer (revealed after dissolve) */
        .content-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px;
            background: linear-gradient(135deg, var(--green-primary), var(--blue-primary), #1a1a2e);
            z-index: 1;
            border-radius: 20px;
        }
        
        /* Main dissolving layer */
        .dissolve-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px;
            background: linear-gradient(135deg, var(--brown-primary), var(--brown-tertiary), var(--gold-tertiary));
            z-index: 2;
            border-radius: 20px;
            transition: all 4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: inset 0 0 50px rgba(255, 255, 255, 0.1);
        }
        
        .dissolving .dissolve-layer {
            opacity: 0;
            transform: scale(0.8) rotateX(15deg);
            filter: blur(8px);
        }
        
        /* Enhanced particles with different types */
        .dissolve-particle {
            position: absolute;
            border-radius: 50%;
            z-index: 3;
            opacity: 0;
            pointer-events: none;
            box-shadow: 0 0 10px currentColor;
        }
        
        .particle-atom {
            background: radial-gradient(circle, var(--gold-tertiary), var(--gold-secondary));
            animation-duration: 3s;
        }
        
        .particle-electron {
            background: radial-gradient(circle, var(--brown-tertiary), var(--brown-secondary));
            animation-duration: 2.5s;
        }
        
        .particle-neutron {
            background: radial-gradient(circle, var(--green-secondary), var(--green-primary));
            animation-duration: 3.5s;
        }
        
        .particle-energy {
            background: radial-gradient(circle, var(--gold-primary), var(--brown-primary));
            animation-duration: 2s;
        }
        
        .dissolving .dissolve-particle {
            animation-name: dissolveParticle;
            animation-fill-mode: forwards;
            animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        /* Crack effects with variations */
        .dissolve-crack {
            position: absolute;
            border-radius: 50%;
            z-index: 3;
            opacity: 0;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.8), transparent);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }
        
        .dissolving .dissolve-crack {
            animation: dissolveCrack 3s forwards;
        }
        
        /* Shockwave effect */
        .shockwave {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border: 2px solid rgba(218, 165, 32, 0.6);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 4;
            opacity: 0;
        }
        
        .dissolving .shockwave {
            animation: shockwaveExpand 3s forwards;
        }
        
        /* Content styling */
        .layer-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 25px;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
            letter-spacing: 2px;
            color: var(--white-primary);
        }
        
        .layer-text {
            font-size: 1.3rem;
            max-width: 80%;
            line-height: 1.8;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
            color: var(--white-secondary);
        }
        
        .revealed-title {
            font-size: 2.2rem;
            color: var(--gold-secondary);
            margin-bottom: 25px;
            text-shadow: 0 0 30px rgba(218, 165, 32, 0.8);
            animation: revealGlow 2s ease-in-out infinite alternate;
        }
        
        .revealed-text {
            font-size: 1.2rem;
            max-width: 85%;
            line-height: 1.7;
            background: rgba(218, 165, 32, 0.1);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(218, 165, 32, 0.3);
            backdrop-filter: blur(10px);
            color: var(--white-primary);
        }
        
        .instructions {
            margin-top: 30px;
            padding: 15px 25px;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 25px;
            font-size: 1.1rem;
            color: var(--gold-tertiary);
            border: 1px solid rgba(218, 165, 32, 0.3);
            backdrop-filter: blur(5px);
        }
        
        /* Timer styling */
        .timer-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            margin-top: 20px;
        }
        
        .timer-display {
            font-size: 4rem;
            font-family: monospace;
            margin-bottom: 20px;
            text-shadow: 0 0 20px var(--gold-secondary);
            color: var(--gold-tertiary);
            letter-spacing: 5px;
        }
        
        .timer-controls {
            display: flex;
            gap: 15px;
            margin-top: 15px;
        }
        
        .timer-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 50px;
            background: linear-gradient(135deg, var(--brown-tertiary), var(--brown-secondary));
            color: var(--white-primary);
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-medium);
        }
        
        .timer-btn:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-heavy);
        }
        
        .timer-btn:active {
            transform: translateY(1px);
        }
        
        .timer-btn.start {
            background: linear-gradient(135deg, var(--green-secondary), var(--green-primary));
        }
        
        .timer-btn.reset {
            background: linear-gradient(135deg, var(--brown-primary), var(--brown-tertiary));
        }
        
        /* Prayer points */
        .prayer-points {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        
        .prayer-point {
            width: var(--prayer-point-size);
            height: var(--prayer-point-size);
            background: radial-gradient(circle, var(--gold-secondary), var(--gold-primary));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 20px var(--gold-tertiary);
            font-size: 1.8rem;
            color: var(--brown-primary);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .prayer-point:hover {
            transform: scale(1.1);
            box-shadow: 0 0 30px var(--gold-tertiary);
        }
        
        .prayer-point.active {
            background: radial-gradient(circle, var(--green-secondary), var(--green-primary));
            color: var(--white-primary);
        }
        
        /* Keyframe animations */
        @keyframes titleGlow {
            from { text-shadow: 0 0 30px rgba(218, 165, 32, 0.3); }
            to { text-shadow: 0 0 50px rgba(139, 69, 19, 0.5); }
        }
        
        @keyframes revealGlow {
            from { text-shadow: 0 0 30px rgba(218, 165, 32, 0.8); }
            to { text-shadow: 0 0 50px rgba(218, 165, 32, 1); }
        }
        
        @keyframes floatParticle {
            0% { transform: translateY(100vh) translateX(0) scale(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) translateX(50px) scale(1); opacity: 0; }
        }
        
        @keyframes dissolveParticle {
            0% {
                opacity: 1;
                transform: translate(0, 0) scale(1) rotate(0deg);
                filter: blur(0px);
            }
            50% {
                opacity: 0.8;
                filter: blur(2px);
            }
            100% {
                opacity: 0;
                transform: translate(var(--tx), var(--ty)) scale(0.1) rotate(360deg);
                filter: blur(8px);
            }
        }
        
        @keyframes dissolveCrack {
            0% {
                opacity: 0.9;
                transform: scale(0.3);
            }
            30% {
                opacity: 1;
                transform: scale(1);
            }
            70% {
                opacity: 0.6;
                transform: scale(1.8);
            }
            100% {
                opacity: 0;
                transform: scale(3);
            }
        }
        
        @keyframes shockwaveExpand {
            0% {
                width: 0;
                height: 0;
                opacity: 0.8;
            }
            50% {
                opacity: 0.4;
            }
            100% {
                width: 600px;
                height: 600px;
                opacity: 0;
            }
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            h1 { font-size: 2.2rem; }
            .molecular-dissolve-container { height: 400px; }
            .layer-title { font-size: 2rem; }
            .layer-text, .revealed-text { font-size: 1.1rem; }
            .timer-display { font-size: 3rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-pray"></i> Prayer Timer</h1>
        <p class="subtitle">Sacred moments with molecular dissolve effect</p>
        
        <div class="molecular-dissolve-container" id="timerContainer">
            <!-- Background particle field -->
            <div class="particle-field" id="particleField"></div>
            
            <!-- Hidden content layer -->
            <div class="content-layer">
                <div class="revealed-title">Prayer Completed</div>
                <p class="revealed-text">
                    Your prayer session has concluded. May your devotion bring peace and blessings. 
                    The molecular barrier dissolves to reveal this moment of spiritual connection.
                </p>
                
                <!-- Timer display -->
                <div class="timer-container">
                    <div class="timer-display" id="timerDisplay">05:00</div>
                    <div class="timer-controls">
                        <button class="timer-btn start" id="startBtn"><i class="fas fa-play"></i> Start</button>
                        <button class="timer-btn reset" id="resetBtn"><i class="fas fa-redo"></i> Reset</button>
                    </div>
                </div>
            </div>
            
            <!-- Dissolving layer -->
            <div class="dissolve-layer">
                <div class="layer-title">PRAYER TIMER</div>
                <p class="layer-text">Click start to begin your prayer session</p>
            </div>
            
            <!-- Shockwave effect -->
            <div class="shockwave"></div>
            
            <!-- Pre-defined particles -->
            <div class="dissolve-particle particle-atom" style="--tx: -120px; --ty: -90px; width: 14px; height: 14px; top: 25%; left: 20%; animation-delay: 0.1s"></div>
            <div class="dissolve-particle particle-electron" style="--tx: 140px; --ty: -70px; width: 8px; height: 8px; top: 40%; left: 75%; animation-delay: 0.3s"></div>
            <div class="dissolve-particle particle-neutron" style="--tx: -90px; --ty: 110px; width: 16px; height: 16px; top: 65%; left: 30%; animation-delay: 0.5s"></div>
            <div class="dissolve-particle particle-energy" style="--tx: 100px; --ty: 80px; width: 10px; height: 10px; top: 30%; left: 65%; animation-delay: 0.7s"></div>
            <div class="dissolve-particle particle-atom" style="--tx: -80px; --ty: -60px; width: 12px; height: 12px; top: 70%; left: 15%; animation-delay: 0.9s"></div>
            <div class="dissolve-particle particle-electron" style="--tx: 90px; --ty: -100px; width: 6px; height: 6px; top: 50%; left: 80%; animation-delay: 1.1s"></div>
            
            <!-- Crack effects -->
            <div class="dissolve-crack" style="width: 20px; height: 20px; top: 45%; left: 50%; animation-delay: 0.4s"></div>
            <div class="dissolve-crack" style="width: 25px; height: 25px; top: 25%; left: 30%; animation-delay: 0.6s"></div>
            <div class="dissolve-crack" style="width: 18px; height: 18px; top: 70%; left: 70%; animation-delay: 0.8s"></div>
        </div>
        
        <div class="prayer-points">
            <div class="prayer-point" data-time="1">1</div>
            <div class="prayer-point" data-time="3">3</div>
            <div class="prayer-point" data-time="5">5</div>
            <div class="prayer-point" data-time="10">10</div>
            <div class="prayer-point" data-time="15">15</div>
        </div>
        
        <div class="instructions">
            <i class="fas fa-info-circle"></i> Select a prayer duration and click start to begin
        </div>
    </div>
    
    <script>
        // DOM elements
        const timerContainer = document.getElementById('timerContainer');
        const timerDisplay = document.getElementById('timerDisplay');
        const startBtn = document.getElementById('startBtn');
        const resetBtn = document.getElementById('resetBtn');
        const prayerPoints = document.querySelectorAll('.prayer-point');
        
        // Timer variables
        let countdown;
        let totalSeconds = 300; // Default 5 minutes
        let remainingSeconds = totalSeconds;
        let isRunning = false;
        
        // Create background particle field
        function createBackgroundParticles() {
            const field = document.getElementById('particleField');
            
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.classList.add('bg-particle');
                
                const size = Math.random() * 4 + 2;
                const left = Math.random() * 100;
                const delay = Math.random() * 8;
                
                particle.style.cssText = `
                    width: ${size}px;
                    height: ${size}px;
                    left: ${left}%;
                    animation-delay: ${delay}s;
                `;
                
                field.appendChild(particle);
            }
        }
        
        // Enhanced particle generation
        function generateParticles(count) {
            for (let i = 0; i < count; i++) {
                const particle = document.createElement('div');
                particle.classList.add('dissolve-particle');
                
                // Random particle type
                const types = ['particle-atom', 'particle-electron', 'particle-neutron', 'particle-energy'];
                const type = types[Math.floor(Math.random() * types.length)];
                particle.classList.add(type);
                
                // Random properties
                const size = Math.random() * 12 + 4;
                const top = Math.random() * 100;
                const left = Math.random() * 100;
                const tx = (Math.random() - 0.5) * 300;
                const ty = (Math.random() - 0.5) * 300;
                const delay = Math.random() * 1.5;
                
                particle.style.cssText = `
                    --tx: ${tx}px;
                    --ty: ${ty}px;
                    width: ${size}px;
                    height: ${size}px;
                    top: ${top}%;
                    left: ${left}%;
                    animation-delay: ${delay}s;
                `;
                
                timerContainer.appendChild(particle);
                
                // Remove particle after animation
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 4000);
            }
        }
        
        // Generate crack effects
        function generateCracks(count) {
            for (let i = 0; i < count; i++) {
                const crack = document.createElement('div');
                crack.classList.add('dissolve-crack');
                
                const size = Math.random() * 20 + 15;
                const top = Math.random() * 100;
                const left = Math.random() * 100;
                const delay = Math.random() * 2;
                
                crack.style.cssText = `
                    width: ${size}px;
                    height: ${size}px;
                    top: ${top}%;
                    left: ${left}%;
                    animation-delay: ${delay}s;
                `;
                
                timerContainer.appendChild(crack);
                
                // Remove crack after animation
                setTimeout(() => {
                    if (crack.parentNode) {
                        crack.parentNode.removeChild(crack);
                    }
                }, 3500);
            }
        }
        
        // Format time as MM:SS
        function formatTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
        }
        
        // Update timer display
        function updateTimer() {
            timerDisplay.textContent = formatTime(remainingSeconds);
            
            // Calculate dissolve effect progress (0 to 1)
            const progress = 1 - (remainingSeconds / totalSeconds);
            
            // Update dissolve effect based on progress
            const dissolveLayer = document.querySelector('.dissolve-layer');
            dissolveLayer.style.opacity = 1 - progress;
            dissolveLayer.style.transform = `scale(${1 - progress * 0.2}) rotateX(${progress * 15}deg)`;
            dissolveLayer.style.filter = `blur(${progress * 8}px)`;
            
            // Generate particles and cracks at certain intervals
            if (progress > 0.2 && progress < 0.9 && Math.random() > 0.7) {
                generateParticles(Math.floor(Math.random() * 3) + 1);
            }
            
            if (progress > 0.3 && progress < 0.8 && Math.random() > 0.8) {
                generateCracks(Math.floor(Math.random() * 2) + 1);
            }
            
            if (remainingSeconds <= 0) {
                clearInterval(countdown);
                isRunning = false;
                startBtn.innerHTML = '<i class="fas fa-play"></i> Start';
                timerContainer.classList.add('dissolving');
            } else {
                remainingSeconds--;
            }
        }
        
        // Start the timer
        function startTimer() {
            if (isRunning) {
                // Pause timer
                clearInterval(countdown);
                isRunning = false;
                startBtn.innerHTML = '<i class="fas fa-play"></i> Start';
            } else {
                // Start timer
                if (remainingSeconds <= 0) {
                    remainingSeconds = totalSeconds;
                    timerContainer.classList.remove('dissolving');
                }
                
                countdown = setInterval(updateTimer, 1000);
                isRunning = true;
                startBtn.innerHTML = '<i class="fas fa-pause"></i> Pause';
            }
        }
        
        // Reset the timer
        function resetTimer() {
            clearInterval(countdown);
            isRunning = false;
            remainingSeconds = totalSeconds;
            timerDisplay.textContent = formatTime(remainingSeconds);
            startBtn.innerHTML = '<i class="fas fa-play"></i> Start';
            timerContainer.classList.remove('dissolving');
            
            // Reset dissolve effect
            const dissolveLayer = document.querySelector('.dissolve-layer');
            dissolveLayer.style.opacity = '1';
            dissolveLayer.style.transform = 'none';
            dissolveLayer.style.filter = 'none';
        }
        
        // Set prayer duration
        function setDuration(minutes) {
            totalSeconds = minutes * 60;
            resetTimer();
            
            // Update active prayer point
            prayerPoints.forEach(point => {
                point.classList.remove('active');
                if (parseInt(point.getAttribute('data-time')) === minutes) {
                    point.classList.add('active');
                }
            });
        }
        
        // Event listeners
        startBtn.addEventListener('click', startTimer);
        resetBtn.addEventListener('click', resetTimer);
        
        prayerPoints.forEach(point => {
            point.addEventListener('click', () => {
                const minutes = parseInt(point.getAttribute('data-time'));
                setDuration(minutes);
            });
        });
        
        // Initialize background particles
        createBackgroundParticles();
        
        // Recreate background particles periodically
        setInterval(createBackgroundParticles, 10000);
    </script>
</body>
</html>
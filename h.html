<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prayer Time Reveal</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
          --gold-primary: #DAA520;
          --gold-secondary: #FFD700;
          --gold-tertiary: #F0E68C;
          --brown-primary: #8B4513;
          --brown-secondary: #A0522D;
          --brown-tertiary: #CD853F;
          --green-primary: #2C5530;
          --green-secondary: #3D8B40;
          --blue-primary: #1a3a5f;
          --white-primary: #FFFDF7;
          --white-secondary: #F8F4E9;
          --shadow-light: 0 4px 20px rgba(0,0,0,0.08);
          --shadow-medium: 0 8px 30px rgba(0,0,0,0.15);
          --shadow-heavy: 0 12px 40px rgba(0,0,0,0.2);
          --prayer-point-size: 60px;
          --atmosphere-opacity: 0.6;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, var(--brown-primary), var(--blue-primary), var(--green-primary));
            color: var(--white-primary);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
            overflow: hidden;
        }
        
        .container {
            max-width: 800px;
            width: 100%;
            text-align: center;
        }
        
        h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, var(--gold-secondary), var(--gold-primary), var(--brown-tertiary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(218, 165, 32, 0.3);
            animation: titleGlow 3s ease-in-out infinite alternate;
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.8;
            color: var(--gold-tertiary);
        }
        
        /* Enhanced Prayer Reveal Container */
        .prayer-reveal-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 20px 0;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 
                0 20px 60px rgba(0, 0, 0, 0.6),
                inset 0 0 30px rgba(255, 255, 255, 0.05);
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .prayer-reveal-container:hover {
            transform: scale(1.02);
        }
        
        /* Background particle field */
        .particle-field {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            opacity: 0.3;
        }
        
        .bg-particle {
            position: absolute;
            background: radial-gradient(circle, rgba(218, 165, 32, 0.6), transparent);
            border-radius: 50%;
            animation: floatParticle 8s infinite linear;
        }
        
        /* Content layer (revealed after dissolve) */
        .content-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px;
            background: linear-gradient(135deg, var(--blue-primary), var(--brown-secondary), var(--green-primary));
            z-index: 1;
            border-radius: 20px;
        }
        
        /* Main dissolving layer */
        .dissolve-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px;
            background: linear-gradient(135deg, var(--brown-tertiary), var(--gold-primary), var(--gold-secondary));
            z-index: 2;
            border-radius: 20px;
            transition: all 4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: inset 0 0 50px rgba(255, 255, 255, 0.1);
        }
        
        .prayer-reveal-container.revealed .dissolve-layer {
            opacity: 0;
            transform: scale(0.8) rotateX(15deg);
            filter: blur(8px);
        }
        
        /* Enhanced particles with different types */
        .dissolve-particle {
            position: absolute;
            border-radius: 50%;
            z-index: 3;
            opacity: 0;
            pointer-events: none;
            box-shadow: 0 0 10px currentColor;
        }
        
        .particle-atom {
            background: radial-gradient(circle, var(--gold-tertiary), var(--gold-secondary));
            animation-duration: 3s;
        }
        
        .particle-electron {
            background: radial-gradient(circle, var(--brown-tertiary), var(--brown-secondary));
            animation-duration: 2.5s;
        }
        
        .particle-neutron {
            background: radial-gradient(circle, var(--green-secondary), var(--green-primary));
            animation-duration: 3.5s;
        }
        
        .particle-energy {
            background: radial-gradient(circle, var(--gold-primary), var(--brown-tertiary));
            animation-duration: 2s;
        }
        
        .prayer-reveal-container.revealed .dissolve-particle {
            animation-name: dissolveParticle;
            animation-fill-mode: forwards;
            animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        /* Crack effects with variations */
        .dissolve-crack {
            position: absolute;
            border-radius: 50%;
            z-index: 3;
            opacity: 0;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.8), transparent);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }
        
        .prayer-reveal-container.revealed .dissolve-crack {
            animation: dissolveCrack 3s forwards;
        }
        
        /* Shockwave effect */
        .shockwave {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border: 2px solid rgba(218, 165, 32, 0.6);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 4;
            opacity: 0;
        }
        
        .prayer-reveal-container.revealed .shockwave {
            animation: shockwaveExpand 3s forwards;
        }
        
        /* Content styling */
        .layer-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 25px;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
            letter-spacing: 2px;
            color: var(--white-primary);
        }
        
        .layer-text {
            font-size: 1.3rem;
            max-width: 80%;
            line-height: 1.8;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
            color: var(--white-secondary);
        }
        
        .revealed-title {
            font-size: 2.2rem;
            color: var(--gold-tertiary);
            margin-bottom: 25px;
            text-shadow: 0 0 30px rgba(218, 165, 32, 0.8);
            animation: revealGlow 2s ease-in-out infinite alternate;
        }
        
        .revealed-text {
            font-size: 1.2rem;
            max-width: 85%;
            line-height: 1.7;
            background: rgba(218, 165, 32, 0.1);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(218, 165, 32, 0.3);
            backdrop-filter: blur(10px);
            color: var(--white-primary);
        }
        
        .instructions {
            margin-top: 30px;
            padding: 15px 25px;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 25px;
            font-size: 1.1rem;
            color: var(--gold-tertiary);
            border: 1px solid rgba(218, 165, 32, 0.3);
            backdrop-filter: blur(5px);
        }
        
        /* Timer styling */
        .prayer-timer {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
            padding: 20px;
            background: rgba(26, 58, 95, 0.6);
            border-radius: 15px;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .timer-unit {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .timer-value {
            font-size: 2.5rem;
            font-weight: bold;
            min-width: 70px;
            color: var(--gold-secondary);
            text-shadow: 0 0 15px rgba(255, 215, 0, 0.7);
        }
        
        .timer-label {
            font-size: 1rem;
            color: var(--gold-tertiary);
            margin-top: 5px;
        }
        
        .prayer-info {
            margin-top: 25px;
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            border-left: 3px solid var(--gold-primary);
        }
        
        .next-prayer {
            color: var(--gold-secondary);
            font-weight: bold;
        }
        
        /* Prayer points */
        .prayer-points {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            margin-top: 30px;
            gap: 15px;
        }
        
        .prayer-point {
            background: rgba(255, 215, 0, 0.1);
            border-radius: 50%;
            width: var(--prayer-point-size);
            height: var(--prayer-point-size);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: var(--gold-tertiary);
            border: 2px solid rgba(218, 165, 32, 0.3);
            transition: all 0.3s ease;
            position: relative;
            box-shadow: var(--shadow-light);
        }
        
        .prayer-point:hover {
            transform: scale(1.1);
            background: rgba(218, 165, 32, 0.2);
            box-shadow: 0 0 20px var(--gold-secondary);
        }
        
        .prayer-point.active {
            background: rgba(218, 165, 32, 0.3);
            color: var(--gold-secondary);
            box-shadow: 0 0 25px var(--gold-primary);
            border-color: var(--gold-tertiary);
        }
        
        .prayer-name {
            position: absolute;
            bottom: -25px;
            font-size: 0.8rem;
            color: var(--gold-tertiary);
            white-space: nowrap;
        }
        
        /* Keyframe animations */
        @keyframes titleGlow {
            from { text-shadow: 0 0 30px rgba(218, 165, 32, 0.3); }
            to { text-shadow: 0 0 50px rgba(139, 69, 19, 0.5); }
        }
        
        @keyframes revealGlow {
            from { text-shadow: 0 0 30px rgba(218, 165, 32, 0.8); }
            to { text-shadow: 0 0 50px rgba(218, 165, 32, 1); }
        }
        
        @keyframes floatParticle {
            0% { transform: translateY(100vh) translateX(0) scale(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) translateX(50px) scale(1); opacity: 0; }
        }
        
        @keyframes dissolveParticle {
            0% {
                opacity: 1;
                transform: translate(0, 0) scale(1) rotate(0deg);
                filter: blur(0px);
            }
            50% {
                opacity: 0.8;
                filter: blur(2px);
            }
            100% {
                opacity: 0;
                transform: translate(var(--tx), var(--ty)) scale(0.1) rotate(360deg);
                filter: blur(8px);
            }
        }
        
        @keyframes dissolveCrack {
            0% {
                opacity: 0.9;
                transform: scale(0.3);
            }
            30% {
                opacity: 1;
                transform: scale(1);
            }
            70% {
                opacity: 0.6;
                transform: scale(1.8);
            }
            100% {
                opacity: 0;
                transform: scale(3);
            }
        }
        
        @keyframes shockwaveExpand {
            0% {
                width: 0;
                height: 0;
                opacity: 0.8;
            }
            50% {
                opacity: 0.4;
            }
            100% {
                width: 600px;
                height: 600px;
                opacity: 0;
            }
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            h1 { font-size: 2.2rem; }
            .prayer-reveal-container { height: 400px; }
            .layer-title { font-size: 2rem; }
            .layer-text, .revealed-text { font-size: 1.1rem; }
            .timer-value { font-size: 1.8rem; min-width: 50px; }
            .prayer-point { 
                --prayer-point-size: 50px; 
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-mosque"></i> Prayer Time Reveal</h1>
        <p class="subtitle">Click to reveal next prayer time</p>
        
        <div class="prayer-reveal-container" id="prayerReveal">
            <!-- Background particle field -->
            <div class="particle-field" id="particleField"></div>
            
            <!-- Hidden content layer -->
            <div class="content-layer">
                <div class="revealed-title">Next Prayer Time</div>
                <div class="prayer-timer" id="prayerTimer">
                    <div class="timer-unit">
                        <div class="timer-value" id="hours">00</div>
                        <div class="timer-label">HOURS</div>
                    </div>
                    <div class="timer-unit">
                        <div class="timer-value" id="minutes">00</div>
                        <div class="timer-label">MINUTES</div>
                    </div>
                    <div class="timer-unit">
                        <div class="timer-value" id="seconds">00</div>
                        <div class="timer-label">SECONDS</div>
                    </div>
                </div>
                
                <div class="prayer-info">
                    Next prayer: <span class="next-prayer" id="nextPrayer">Maghrib</span> at <span id="prayerTime">18:45</span>
                </div>
                
                <div class="prayer-points">
                    <div class="prayer-point">
                        <i class="fas fa-sun"></i>
                        <span class="prayer-name">Fajr</span>
                    </div>
                    <div class="prayer-point">
                        <i class="fas fa-sun"></i>
                        <span class="prayer-name">Dhuhr</span>
                    </div>
                    <div class="prayer-point active">
                        <i class="fas fa-sun"></i>
                        <span class="prayer-name">Asr</span>
                    </div>
                    <div class="prayer-point">
                        <i class="fas fa-moon"></i>
                        <span class="prayer-name">Maghrib</span>
                    </div>
                    <div class="prayer-point">
                        <i class="fas fa-moon"></i>
                        <span class="prayer-name">Isha</span>
                    </div>
                </div>
            </div>
            
            <!-- Dissolving layer -->
            <div class="dissolve-layer">
                <div class="layer-title">PRAYER TIME AWAITS</div>
                <p class="layer-text">Click to reveal next prayer time</p>
            </div>
            
            <!-- Shockwave effect -->
            <div class="shockwave"></div>
            
            <!-- Pre-defined particles -->
            <div class="dissolve-particle particle-atom" style="--tx: -120px; --ty: -90px; width: 14px; height: 14px; top: 25%; left: 20%; animation-delay: 0.1s"></div>
            <div class="dissolve-particle particle-electron" style="--tx: 140px; --ty: -70px; width: 8px; height: 8px; top: 40%; left: 75%; animation-delay: 0.3s"></div>
            <div class="dissolve-particle particle-neutron" style="--tx: -90px; --ty: 110px; width: 16px; height: 16px; top: 65%; left: 30%; animation-delay: 0.5s"></div>
            <div class="dissolve-particle particle-energy" style="--tx: 100px; --ty: 80px; width: 10px; height: 10px; top: 30%; left: 65%; animation-delay: 0.7s"></div>
            <div class="dissolve-particle particle-atom" style="--tx: -80px; --ty: -60px; width: 12px; height: 12px; top: 70%; left: 15%; animation-delay: 0.9s"></div>
            <div class="dissolve-particle particle-electron" style="--tx: 90px; --ty: -100px; width: 6px; height: 6px; top: 50%; left: 80%; animation-delay: 1.1s"></div>
            
            <!-- Crack effects -->
            <div class="dissolve-crack" style="width: 20px; height: 20px; top: 45%; left: 50%; animation-delay: 0.4s"></div>
            <div class="dissolve-crack" style="width: 25px; height: 25px; top: 25%; left: 30%; animation-delay: 0.6s"></div>
            <div class="dissolve-crack" style="width: 18px; height: 18px; top: 70%; left: 70%; animation-delay: 0.8s"></div>
        </div>
        
        <div class="instructions">
            <i class="fas fa-hand-pointer"></i> Click to reveal next prayer time
        </div>
    </div>
    
    <script>
        // Create background particle field
        function createBackgroundParticles() {
            const field = document.getElementById('particleField');
            
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.classList.add('bg-particle');
                
                const size = Math.random() * 4 + 2;
                const left = Math.random() * 100;
                const delay = Math.random() * 8;
                
                particle.style.cssText = `
                    width: ${size}px;
                    height: ${size}px;
                    left: ${left}%;
                    animation-delay: ${delay}s;
                `;
                
                field.appendChild(particle);
            }
        }
        
        // Enhanced particle generation on click
        function createRevealParticles() {
            const container = document.getElementById('prayerReveal');
            
            // Create burst of random particles
            for (let i = 0; i < 25; i++) {
                const particle = document.createElement('div');
                particle.classList.add('dissolve-particle');
                
                // Random particle type
                const types = ['particle-atom', 'particle-electron', 'particle-neutron', 'particle-energy'];
                const type = types[Math.floor(Math.random() * types.length)];
                particle.classList.add(type);
                
                // Random properties
                const size = Math.random() * 12 + 4;
                const top = Math.random() * 100;
                const left = Math.random() * 100;
                const tx = (Math.random() - 0.5) * 300;
                const ty = (Math.random() - 0.5) * 300;
                const delay = Math.random() * 1.5;
                
                particle.style.cssText = `
                    --tx: ${tx}px;
                    --ty: ${ty}px;
                    width: ${size}px;
                    height: ${size}px;
                    top: ${top}%;
                    left: ${left}%;
                    animation-delay: ${delay}s;
                `;
                
                container.appendChild(particle);
                
                // Remove particle after animation
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 4000);
            }
            
            // Create additional crack effects
            for (let i = 0; i < 8; i++) {
                const crack = document.createElement('div');
                crack.classList.add('dissolve-crack');
                
                const size = Math.random() * 20 + 15;
                const top = Math.random() * 100;
                const left = Math.random() * 100;
                const delay = Math.random() * 2;
                
                crack.style.cssText = `
                    width: ${size}px;
                    height: ${size}px;
                    top: ${top}%;
                    left: ${left}%;
                    animation-delay: ${delay}s;
                `;
                
                container.appendChild(crack);
                
                // Remove crack after animation
                setTimeout(() => {
                    if (crack.parentNode) {
                        crack.parentNode.removeChild(crack);
                    }
                }, 3500);
            }
        }
        
        // Initialize background particles
        createBackgroundParticles();
        
        // Recreate background particles periodically
        setInterval(createBackgroundParticles, 10000);
        
        // Prayer timer functionality
        function updatePrayerTimer() {
            // Set target time (for demo, 1 hour 15 minutes from now)
            const now = new Date();
            const targetTime = new Date();
            targetTime.setHours(now.getHours() + 1);
            targetTime.setMinutes(now.getMinutes() + 15);
            
            const diff = targetTime - now;
            
            if (diff <= 0) {
                // Reset timer if target time passed
                targetTime.setDate(targetTime.getDate() + 1);
                return updatePrayerTimer();
            }
            
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);
            
            document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
            document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
            
            // Update prayer time display
            const prayerTime = targetTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            document.getElementById('prayerTime').textContent = prayerTime;
        }
        
        // Initialize timer
        updatePrayerTimer();
        setInterval(updatePrayerTimer, 1000);
        
        // Handle reveal effect on click
        document.getElementById('prayerReveal').addEventListener('click', function() {
            if (!this.classList.contains('revealed')) {
                this.classList.add('revealed');
                createRevealParticles();
                
                // Update instructions
                document.querySelector('.instructions').innerHTML = 
                    '<i class="fas fa-clock"></i> Next prayer time revealed';
            }
        });
    </script>
</body>
</html>